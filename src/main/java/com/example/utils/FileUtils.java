package com.example.utils;

import org.springframework.web.multipart.MultipartFile;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class FileUtils {

    private static final Set<String> ALLOWED_IMAGE_TYPES = new HashSet<>(
            Arrays.asList("image/jpeg", "image/png", "image/gif", "image/bmp")
    );

    public static String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex == -1 ? "" : fileName.substring(lastDotIndex + 1).toLowerCase();
    }

    public static boolean isImage(String contentType) {
        return ALLOWED_IMAGE_TYPES.contains(contentType);
    }

    public static String getRemoteIp() {
        try {
            return InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            return "127.0.0.1";
        }
    }

    public static void compressImage(Path source, Path target, int maxWidth, int maxHeight) throws IOException {
        // 使用imgscalr库压缩图片
        BufferedImage resizedImage = org.imgscalr.Scalr.resize(
                javax.imageio.ImageIO.read(source.toFile()),
                org.imgscalr.Scalr.Method.AUTOMATIC,
                org.imgscalr.Scalr.Mode.AUTOMATIC,
                maxWidth,
                maxHeight
        );
        try {
            ImageIO.write(resizedImage, "png", target.toFile());
        } catch (IOException e) {
            throw new IOException("Failed to write resized image to file");
        }
    }

    public static void deleteFile(String filePath) {
        try {
            Files.deleteIfExists(Paths.get(filePath));
        } catch (IOException e) {
            // 记录日志
        }
    }

    public static void cleanUpOldFiles(String directoryPath, int daysToKeep) {
        try {
            Files.walk(Paths.get(directoryPath))
                    .filter(path -> !Files.isDirectory(path))
                    .filter(path -> {
                        try {
                            return Files.getLastModifiedTime(path).toInstant()
                                    .isBefore(java.time.Instant.now().minusSeconds(daysToKeep * 24 * 60 * 60));
                        } catch (IOException e) {
                            return false;
                        }
                    })
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                        } catch (IOException e) {
                            // 记录日志
                        }
                    });
        } catch (IOException e) {
            // 记录日志
        }
    }
}
